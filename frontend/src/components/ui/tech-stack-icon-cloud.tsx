"use client"

import React, { useState, useEffect } from 'react'
import IconCloud from './icon-cloud'
import { API_BASE_URL } from '@/lib/api'

interface TechStackIconCloudProps {
  className?: string
}

// 默认技术栈图标（保持与原硬编码一致）
const DEFAULT_TECH_ICONS = [
  "openai", "claude", "anthropic", "gemini", "meta", "mistral", "huggingface",
  "qwen", "doubao", "hunyuan", "zhipu", "deepseek",
  "langchain", "modelscope", "cursor", "ollama", "openrouter", "github"
]

export default function TechStackIconCloud({ className }: TechStackIconCloudProps) {
  const [iconSlugs, setIconSlugs] = useState<string[]>(DEFAULT_TECH_ICONS)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchTechIcons = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/site-settings/tech-icons/visible`, {
          cache: 'no-store' // 确保获取最新数据
        })
        
        if (response.ok) {
          const data = await response.json()
          if (data.tech_icons && Array.isArray(data.tech_icons) && data.tech_icons.length > 0) {
            setIconSlugs(data.tech_icons)
          } else {
            // 如果API返回空数据，使用默认图标
            console.warn('API returned empty tech icons, using defaults')
            setIconSlugs(DEFAULT_TECH_ICONS)
          }
        } else {
          console.warn('Failed to fetch tech icons from API, using defaults')
          setIconSlugs(DEFAULT_TECH_ICONS)
        }
      } catch (error) {
        console.warn('Error fetching tech icons:', error)
        // 网络错误时使用默认图标，确保用户体验不受影响
        setIconSlugs(DEFAULT_TECH_ICONS)
      } finally {
        setLoading(false)
      }
    }

    fetchTechIcons()
  }, [])

  // 加载状态显示
  if (loading) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="animate-pulse space-y-4 text-center">
          <div className="w-32 h-32 bg-muted rounded-full mx-auto"></div>
          <div className="h-4 bg-muted rounded w-32 mx-auto"></div>
          <div className="text-sm text-muted-foreground">Loading tech stack...</div>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <IconCloud iconSlugs={iconSlugs} />
    </div>
  )
}
