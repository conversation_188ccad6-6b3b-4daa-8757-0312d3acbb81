'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Image from 'next/image'
import { cn } from '@/utils/cn'
import { Eye, Download, Calendar, Tag, Folder, Heart, Share2, Grid3X3, LayoutGrid, Play, Pause, ChevronLeft, ChevronRight } from 'lucide-react'
import { API_BASE_URL } from '@/lib/api'

interface ImageData {
  id: number
  url: string
  thumbnail_url?: string
  display_name?: string
  description?: string
  original_filename: string
  width: number
  height: number
  category?: {
    id: number
    name: string
    color: string
    icon?: string
  }
  tags: Array<{
    id: number
    name: string
  }>
  usage_type: string
  created_at: string
}

type LayoutType = 'grid' | 'masonry' | 'carousel'

interface GalleryGridProps {
  images: ImageData[]
  loading: boolean
  searchQuery: string
  selectedCategory: number | null
  selectedTags: string[]
  layout?: LayoutType
}

export function GalleryGrid({
  images,
  loading,
  searchQuery,
  selectedCategory,
  selectedTags,
  layout = 'grid'
}: GalleryGridProps) {
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null)
  const [imageLoadErrors, setImageLoadErrors] = useState<Set<number>>(new Set())
  const [currentLayout, setCurrentLayout] = useState<LayoutType>(layout)
  const [visibleImages, setVisibleImages] = useState<Set<number>>(new Set())
  const [carouselIndex, setCarouselIndex] = useState(0)
  const [isCarouselPlaying, setIsCarouselPlaying] = useState(false)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const carouselIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // 懒加载逻辑
  const imageRefs = useRef<Map<number, HTMLDivElement>>(new Map())

  const setImageRef = useCallback((id: number) => (el: HTMLDivElement | null) => {
    if (el) {
      imageRefs.current.set(id, el)
    } else {
      imageRefs.current.delete(id)
    }
  }, [])

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const imageId = parseInt(entry.target.getAttribute('data-image-id') || '0')
            if (imageId) {
              setVisibleImages(prev => new Set(prev).add(imageId))
            }
          }
        })
      },
      {
        rootMargin: '100px',
        threshold: 0.1
      }
    )

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  useEffect(() => {
    const observer = observerRef.current
    if (!observer) return

    // 延迟观察，确保DOM元素已经渲染
    const timeoutId = setTimeout(() => {
      imageRefs.current.forEach((element) => {
        observer.observe(element)
      })
    }, 100)

    return () => {
      clearTimeout(timeoutId)
      imageRefs.current.forEach((element) => {
        observer.unobserve(element)
      })
    }
  }, [images])

  // 初始化时加载前几张图片
  useEffect(() => {
    if (images.length > 0) {
      const initialImages = images.slice(0, 4).map(img => img.id)
      setVisibleImages(prev => {
        const newSet = new Set(prev)
        initialImages.forEach(id => newSet.add(id))
        return newSet
      })
    }
  }, [images])

  const getImageUrl = (image: ImageData): string => {
    const baseUrl = 'http://100.90.150.110:8000'

    if (!image) return ''

    let imageUrl = image.thumbnail_url || image.url
    if (!imageUrl) return ''

    // 如果已经是完整URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl
    }

    // 确保URL以/开头
    if (!imageUrl.startsWith('/')) {
      imageUrl = '/' + imageUrl
    }

    return `${baseUrl}${imageUrl}`
  }

  const getFullImageUrl = (image: ImageData): string => {
    const baseUrl = API_BASE_URL.replace('/api', '')

    if (!image || !image.url) return ''

    let imageUrl = image.url

    // 如果已经是完整URL，直接返回
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl
    }

    // 确保URL以/开头
    if (!imageUrl.startsWith('/')) {
      imageUrl = '/' + imageUrl
    }

    return `${baseUrl}${imageUrl}`
  }

  const handleImageError = (imageId: number) => {
    setImageLoadErrors(prev => new Set(prev).add(imageId))
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleDownload = (image: ImageData) => {
    const link = document.createElement('a')
    link.href = getFullImageUrl(image)
    link.download = image.display_name || image.original_filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleShare = async (image: ImageData) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: image.display_name || image.original_filename,
          text: image.description || '',
          url: getFullImageUrl(image)
        })
      } catch (error) {
        // Share cancelled
      }
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(getFullImageUrl(image))
      // 这里可以添加一个提示
    }
  }

  // 轮播功能
  const nextImage = () => {
    setCarouselIndex((prev) => (prev + 1) % images.length)
  }

  const prevImage = () => {
    setCarouselIndex((prev) => (prev - 1 + images.length) % images.length)
  }

  const toggleCarouselPlay = () => {
    setIsCarouselPlaying(!isCarouselPlaying)
  }

  useEffect(() => {
    if (isCarouselPlaying && currentLayout === 'carousel') {
      carouselIntervalRef.current = setInterval(nextImage, 3000)
    } else {
      if (carouselIntervalRef.current) {
        clearInterval(carouselIntervalRef.current)
        carouselIntervalRef.current = null
      }
    }

    return () => {
      if (carouselIntervalRef.current) {
        clearInterval(carouselIntervalRef.current)
      }
    }
  }, [isCarouselPlaying, currentLayout, images.length])

  // 布局切换
  const handleLayoutChange = (newLayout: LayoutType) => {
    setCurrentLayout(newLayout)
    setIsCarouselPlaying(false)
    setCarouselIndex(0)
  }

  // 懒加载图片组件
  const LazyImage = ({ image, className, onClick }: {
    image: ImageData,
    className?: string,
    onClick?: () => void
  }) => {
    const isVisible = visibleImages.has(image.id)
    const hasError = imageLoadErrors.has(image.id)
    const imageUrl = getImageUrl(image)

    return (
      <div
        ref={setImageRef(image.id)}
        data-image-id={image.id}
        className={cn("relative overflow-hidden", className)}
        onClick={onClick}
      >
        {isVisible && !hasError && imageUrl ? (
          <Image
            src={imageUrl}
            alt={image.display_name || image.original_filename || 'Gallery image'}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-110"
            onError={() => handleImageError(image.id)}
            unoptimized={true}
            priority={false}
          />
        ) : hasError || !imageUrl ? (
          <div className="w-full h-full flex items-center justify-center bg-muted">
            <Eye className="w-8 h-8 text-muted-foreground" />
          </div>
        ) : (
          <div className="w-full h-full bg-muted animate-pulse flex items-center justify-center">
            <div className="text-xs text-muted-foreground">加载中...</div>
          </div>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {/* 布局选择器骨架 */}
        <div className="flex justify-end">
          <div className="w-48 h-10 bg-muted animate-pulse rounded-md" />
        </div>
        {/* 图片网格骨架 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="aspect-square bg-muted animate-pulse rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  if (images.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <Eye className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">没有找到图片</h3>
        <p className="text-muted-foreground">
          {searchQuery || selectedCategory || selectedTags.length > 0
            ? '尝试调整搜索条件或清除过滤器'
            : '还没有上传任何图片到相册'}
        </p>
      </div>
    )
  }

  // 渲染网格布局
  const renderGridLayout = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {images.map((image) => (
        <div
          key={image.id}
          className="group relative aspect-square overflow-hidden rounded-lg bg-muted cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02]"
          onClick={() => setSelectedImage(image)}
        >
          <LazyImage
            image={image}
            className="w-full h-full"
          />

          {/* 悬停覆盖层 */}
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-between p-4">
            {/* 顶部信息 */}
            <div className="flex justify-between items-start">
              <div className="flex-1">
                {image.category && (
                  <div className="flex items-center mb-2">
                    <Folder className="w-3 h-3 mr-1" style={{ color: image.category.color }} />
                    <span className="text-xs text-white/90">{image.category.name}</span>
                  </div>
                )}
                <h3 className="text-sm font-medium text-white line-clamp-2">
                  {image.display_name || image.original_filename}
                </h3>
              </div>
            </div>

            {/* 底部信息和操作 */}
            <div>
              {/* 标签 */}
              {image.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {image.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag.id}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-white/20 text-white"
                    >
                      <Tag className="w-2 h-2 mr-1" />
                      {tag.name}
                    </span>
                  ))}
                  {image.tags.length > 3 && (
                    <span className="text-xs text-white/70">+{image.tags.length - 3}</span>
                  )}
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex justify-between items-center">
                <div className="flex items-center text-xs text-white/70">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(image.created_at)}
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleShare(image)
                    }}
                    className="p-1.5 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                  >
                    <Share2 className="w-3 h-3 text-white" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDownload(image)
                    }}
                    className="p-1.5 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                  >
                    <Download className="w-3 h-3 text-white" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  // 渲染瀑布流布局
  const renderMasonryLayout = () => (
    <div className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 gap-6 space-y-6">
      {images.map((image) => (
        <div
          key={image.id}
          className="group relative break-inside-avoid mb-6 overflow-hidden rounded-lg bg-muted cursor-pointer transition-all duration-300 hover:shadow-lg"
          onClick={() => setSelectedImage(image)}
        >
          <div style={{ aspectRatio: `${image.width}/${image.height}` }}>
            <LazyImage
              image={image}
              className="w-full h-full"
            />
          </div>

          {/* 悬停覆盖层 */}
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-between p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                {image.category && (
                  <div className="flex items-center mb-2">
                    <Folder className="w-3 h-3 mr-1" style={{ color: image.category.color }} />
                    <span className="text-xs text-white/90">{image.category.name}</span>
                  </div>
                )}
                <h3 className="text-sm font-medium text-white line-clamp-2">
                  {image.display_name || image.original_filename}
                </h3>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center text-xs text-white/70">
                <Calendar className="w-3 h-3 mr-1" />
                {formatDate(image.created_at)}
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleShare(image)
                  }}
                  className="p-1.5 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                >
                  <Share2 className="w-3 h-3 text-white" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDownload(image)
                  }}
                  className="p-1.5 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                >
                  <Download className="w-3 h-3 text-white" />
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  // 渲染轮播布局
  const renderCarouselLayout = () => {
    if (images.length === 0) return null

    const currentImage = images[carouselIndex]

    return (
      <div className="relative">
        {/* 轮播控制 */}
        <div className="flex justify-center items-center gap-4 mb-6">
          <button
            onClick={prevImage}
            className="p-2 rounded-full bg-background border border-input hover:bg-accent transition-colors"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>

          <button
            onClick={toggleCarouselPlay}
            className="p-2 rounded-full bg-background border border-input hover:bg-accent transition-colors"
          >
            {isCarouselPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </button>

          <button
            onClick={nextImage}
            className="p-2 rounded-full bg-background border border-input hover:bg-accent transition-colors"
          >
            <ChevronRight className="w-4 h-4" />
          </button>

          <span className="text-sm text-muted-foreground">
            {carouselIndex + 1} / {images.length}
          </span>
        </div>

        {/* 主图片 */}
        <div className="relative aspect-video max-w-4xl mx-auto rounded-lg overflow-hidden bg-muted">
          <LazyImage
            image={currentImage}
            className="w-full h-full cursor-pointer"
            onClick={() => setSelectedImage(currentImage)}
          />

          {/* 图片信息覆盖层 */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
            <h3 className="text-lg font-semibold text-white mb-2">
              {currentImage.display_name || currentImage.original_filename}
            </h3>
            {currentImage.description && (
              <p className="text-sm text-white/80 mb-3">{currentImage.description}</p>
            )}
            <div className="flex justify-between items-center">
              <div className="flex items-center text-sm text-white/70">
                <Calendar className="w-4 h-4 mr-1" />
                {formatDate(currentImage.created_at)}
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleShare(currentImage)
                  }}
                  className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                >
                  <Share2 className="w-4 h-4 text-white" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDownload(currentImage)
                  }}
                  className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                >
                  <Download className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 缩略图导航 */}
        <div className="flex justify-center mt-6 gap-2 overflow-x-auto pb-2">
          {images.map((image, index) => (
            <button
              key={image.id}
              onClick={() => setCarouselIndex(index)}
              className={cn(
                "relative w-16 h-16 rounded-md overflow-hidden border-2 transition-all flex-shrink-0",
                index === carouselIndex
                  ? "border-primary shadow-lg scale-110"
                  : "border-transparent hover:border-muted-foreground"
              )}
            >
              <LazyImage
                image={image}
                className="w-full h-full"
              />
            </button>
          ))}
        </div>
      </div>
    )
  }

  return (
    <>
      {/* 布局选择器 */}
      <div className="flex justify-end mb-6">
        <div className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
          <button
            onClick={() => handleLayoutChange('grid')}
            className={cn(
              'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
              currentLayout === 'grid'
                ? 'bg-background text-foreground shadow-sm'
                : 'hover:bg-background/50 hover:text-foreground'
            )}
          >
            <Grid3X3 className="w-4 h-4 mr-2" />
            网格
          </button>
          <button
            onClick={() => handleLayoutChange('masonry')}
            className={cn(
              'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
              currentLayout === 'masonry'
                ? 'bg-background text-foreground shadow-sm'
                : 'hover:bg-background/50 hover:text-foreground'
            )}
          >
            <LayoutGrid className="w-4 h-4 mr-2" />
            瀑布流
          </button>
          <button
            onClick={() => handleLayoutChange('carousel')}
            className={cn(
              'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
              currentLayout === 'carousel'
                ? 'bg-background text-foreground shadow-sm'
                : 'hover:bg-background/50 hover:text-foreground'
            )}
          >
            <Play className="w-4 h-4 mr-2" />
            轮播
          </button>
        </div>
      </div>

      {/* 渲染对应布局 */}
      {currentLayout === 'grid' && renderGridLayout()}
      {currentLayout === 'masonry' && renderMasonryLayout()}
      {currentLayout === 'carousel' && renderCarouselLayout()}

      {/* 图片预览模态框 */}
      {selectedImage && (
        <div
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div
            className="relative max-w-4xl max-h-full bg-background rounded-lg overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative">
              <Image
                src={getFullImageUrl(selectedImage)}
                alt={selectedImage.display_name || selectedImage.original_filename}
                width={selectedImage.width}
                height={selectedImage.height}
                className="max-w-full max-h-[80vh] object-contain"
              />
              
              {/* 关闭按钮 */}
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-4 right-4 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
              >
                ×
              </button>
            </div>

            {/* 图片信息 */}
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-xl font-semibold mb-2">
                    {selectedImage.display_name || selectedImage.original_filename}
                  </h2>
                  {selectedImage.description && (
                    <p className="text-muted-foreground mb-3">{selectedImage.description}</p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleShare(selectedImage)}
                    className="p-2 rounded-full border border-input hover:bg-accent transition-colors"
                  >
                    <Share2 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDownload(selectedImage)}
                    className="p-2 rounded-full border border-input hover:bg-accent transition-colors"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {formatDate(selectedImage.created_at)}
                </div>
                <div>
                  {selectedImage.width} × {selectedImage.height}
                </div>
                {selectedImage.category && (
                  <div className="flex items-center">
                    <Folder className="w-4 h-4 mr-1" style={{ color: selectedImage.category.color }} />
                    {selectedImage.category.name}
                  </div>
                )}
              </div>

              {selectedImage.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-4">
                  {selectedImage.tags.map((tag) => (
                    <span
                      key={tag.id}
                      className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-muted"
                    >
                      <Tag className="w-3 h-3 mr-1" />
                      {tag.name}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  )
}
