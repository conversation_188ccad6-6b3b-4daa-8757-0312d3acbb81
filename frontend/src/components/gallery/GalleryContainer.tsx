'use client'

import { useState, useEffect } from 'react'
import { Albums } from './Albums'
import { Timeline } from './Timeline'
import { GalleryGrid } from './GalleryGrid'
import { GalleryFilters } from './GalleryFilters'
import { AlbumData, TimelineEntry } from '@/utils/galleryUtils'
import { CalendarDays, FolderOpen, Grid3X3, Search, Filter } from 'lucide-react'
import { cn } from '@/utils/cn'
import { API_BASE_URL } from '@/lib/api'

interface ImageData {
  id: number
  url: string
  thumbnail_url?: string
  display_name?: string
  description?: string
  original_filename: string
  width: number
  height: number
  category?: {
    id: number
    name: string
    color: string
    icon?: string
  }
  tags: Array<{
    id: number
    name: string
  }>
  usage_type: string
  created_at: string
}

type GalleryContainerProps = {
  albums?: AlbumData[]
  timelineEntries?: TimelineEntry[]
  initialView?: 'timeline' | 'albums' | 'grid'
}

export function GalleryContainer({
  albums = [],
  timelineEntries = [],
  initialView = 'timeline'
}: GalleryContainerProps) {
  const [activeView, setActiveView] = useState<'timeline' | 'albums' | 'grid'>(initialView)
  const [images, setImages] = useState<ImageData[]>([])
  const [filteredImages, setFilteredImages] = useState<ImageData[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  
  // 获取Gallery数据
  useEffect(() => {
    const fetchGalleryData = async () => {
      if (activeView !== 'grid') return

      setLoading(true)
      try {
        const response = await fetch(`${API_BASE_URL.replace('/api', '')}/api/gallery/public`)

        if (response.ok) {
          const data = await response.json()

          // 转换Gallery items为图片格式
          const galleryImages = data.featured_items.map((item: any) => {
            const image = item.image
            const baseUrl = API_BASE_URL.replace('/api', '')

            return {
              id: item.id,
              url: image.url.startsWith('http') ? image.url : `${baseUrl}${image.url}`,
              thumbnail_url: image.thumbnail_url ?
                (image.thumbnail_url.startsWith('http') ? image.thumbnail_url : `${baseUrl}${image.thumbnail_url}`) :
                (image.url.startsWith('http') ? image.url : `${baseUrl}${image.url}`),
              display_name: item.title || image.display_name,
              description: item.description,
              original_filename: image.original_filename,
              width: image.width || 1200,
              height: image.height || 800,
              category: {
                id: item.category?.id,
                name: item.category?.name,
                color: '#1890ff'
              },
              tags: [],
              usage_type: 'gallery',
              created_at: new Date().toISOString()
            }
          })

          setImages(galleryImages)
        }
      } catch (error) {
        // Failed to fetch gallery data (silently handled in production)
      } finally {
        setLoading(false)
      }
    }

    fetchGalleryData()
  }, [activeView])

  // 获取Gallery分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(`${API_BASE_URL.replace('/api', '')}/api/gallery/public`)
        if (response.ok) {
          const data = await response.json()
          setCategories(data.categories || [])
        }
      } catch (error) {
        // Failed to fetch gallery categories (silently handled in production)
      }
    }

    fetchCategories()
  }, [])

  // URL参数处理
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const viewParam = urlParams.get('view') as 'timeline' | 'albums' | 'grid' | null

    if (viewParam && ['timeline', 'albums', 'grid'].includes(viewParam)) {
      setActiveView(viewParam)
    }
  }, [])

  // 图片过滤逻辑
  useEffect(() => {
    let filtered = [...images]

    // 搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(image =>
        (image.display_name?.toLowerCase().includes(query)) ||
        (image.description?.toLowerCase().includes(query)) ||
        (image.original_filename.toLowerCase().includes(query)) ||
        (image.tags.some(tag => tag.name.toLowerCase().includes(query)))
      )
    }

    // 分类过滤
    if (selectedCategory) {
      filtered = filtered.filter(image => image.category?.id === selectedCategory)
    }

    // 标签过滤
    if (selectedTags.length > 0) {
      filtered = filtered.filter(image =>
        selectedTags.every(tagName =>
          image.tags.some(tag => tag.name === tagName)
        )
      )
    }

    setFilteredImages(filtered)
  }, [images, searchQuery, selectedCategory, selectedTags])

  // 切换视图时更新URL
  const handleViewChange = (view: 'timeline' | 'albums' | 'grid') => {
    setActiveView(view)

    // 更新URL参数而不刷新页面
    const url = new URL(window.location.href)
    url.searchParams.set('view', view)
    window.history.pushState({}, '', url)
  }

  // 处理搜索
  const handleSearch = async (query: string) => {
    setSearchQuery(query)

    if (!query.trim()) {
      // 如果搜索为空，重新加载默认数据
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/api/gallery/public`)
      if (response.ok) {
        const data = await response.json()
        const galleryImages = data.featured_items.map((item: any) => {
          const image = item.image
          const baseUrl = API_BASE_URL.replace('/api', '')

          return {
            id: item.id,
            url: image.url.startsWith('http') ? image.url : `${baseUrl}${image.url}`,
            thumbnail_url: image.thumbnail_url ?
              (image.thumbnail_url.startsWith('http') ? image.thumbnail_url : `${baseUrl}${image.thumbnail_url}`) :
              (image.url.startsWith('http') ? image.url : `${baseUrl}${image.url}`),
            display_name: item.title || image.display_name,
            description: item.description,
            original_filename: image.original_filename,
            width: image.width || 1200,
            height: image.height || 800,
            category: {
              id: item.category?.id,
              name: item.category?.name,
              color: '#1890ff'
            },
            tags: [],
            usage_type: 'gallery',
            created_at: new Date().toISOString()
          }
        })
        setImages(galleryImages)
      }
      return
    }

    // 执行搜索
    setLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/api/gallery/search?q=${encodeURIComponent(query)}&page=1&page_size=50`)

      if (response.ok) {
        const data = await response.json()

        const searchImages = data.items.map((item: any) => {
          const image = item.image
          const baseUrl = API_BASE_URL.replace('/api', '')

          return {
            id: item.id,
            url: image.url.startsWith('http') ? image.url : `${baseUrl}${image.url}`,
            thumbnail_url: image.thumbnail_url ?
              (image.thumbnail_url.startsWith('http') ? image.thumbnail_url : `${baseUrl}${image.thumbnail_url}`) :
              (image.url.startsWith('http') ? image.url : `${baseUrl}${image.url}`),
            display_name: item.title || image.display_name,
            description: item.description,
            original_filename: image.original_filename,
            width: image.width || 1200,
            height: image.height || 800,
            category: item.category ? {
              id: item.category.id,
              name: item.category.name,
              color: '#1890ff'
            } : null,
            tags: [],
            usage_type: 'gallery',
            created_at: item.created_at
          }
        })

        setImages(searchImages)
      }
    } catch (error) {
      // Search failed (silently handled in production)
    } finally {
      setLoading(false)
    }
  }

  // 处理分类选择
  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId)
  }

  // 处理标签选择
  const handleTagSelect = (tags: string[]) => {
    setSelectedTags(tags)
  }

  // 清除所有过滤器
  const handleClearFilters = () => {
    setSearchQuery('')
    setSelectedCategory(null)
    setSelectedTags([])
  }

  return (
    <div className="container mx-auto py-8 px-4 sm:px-6">
      <div className="w-full">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <h1 className="text-3xl font-bold">Photo Gallery</h1>

          <div className="flex items-center gap-4">
            {/* 搜索和过滤器 */}
            {activeView === 'grid' && (
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索图片..."
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                  />
                </div>
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={cn(
                    'inline-flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium transition-colors',
                    showFilters
                      ? 'bg-primary text-primary-foreground'
                      : 'border border-input bg-background hover:bg-accent hover:text-accent-foreground'
                  )}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  过滤器
                </button>
              </div>
            )}

            {/* 视图切换 */}
            <div className="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground">
              <button
                onClick={() => handleViewChange('timeline')}
                className={cn(
                  'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
                  activeView === 'timeline'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'hover:bg-background/50 hover:text-foreground'
                )}
              >
                <CalendarDays className="w-4 h-4 mr-2" />
                Timeline
              </button>
              <button
                onClick={() => handleViewChange('albums')}
                className={cn(
                  'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
                  activeView === 'albums'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'hover:bg-background/50 hover:text-foreground'
                )}
              >
                <FolderOpen className="w-4 h-4 mr-2" />
                Albums
              </button>
              <button
                onClick={() => handleViewChange('grid')}
                className={cn(
                  'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all',
                  activeView === 'grid'
                    ? 'bg-background text-foreground shadow-sm'
                    : 'hover:bg-background/50 hover:text-foreground'
                )}
              >
                <Grid3X3 className="w-4 h-4 mr-2" />
                Grid
              </button>
            </div>
          </div>
        </div>

        {/* 过滤器面板 */}
        {activeView === 'grid' && showFilters && (
          <div className="mb-6">
            <GalleryFilters
              categories={categories}
              selectedCategory={selectedCategory}
              selectedTags={selectedTags}
              onCategorySelect={handleCategorySelect}
              onTagSelect={handleTagSelect}
              onClearFilters={handleClearFilters}
              images={images}
            />
          </div>
        )}
        
        {activeView === 'timeline' && (
          <div className="mt-0">
            {timelineEntries.length > 0 ? (
              <Timeline entries={timelineEntries} />
            ) : (
              <div className="py-12 text-center">
                <p className="text-muted-foreground">No timeline entries yet</p>
              </div>
            )}
          </div>
        )}

        {activeView === 'albums' && (
          <div className="mt-0">
            {albums.length > 0 ? (
              <Albums albums={albums} />
            ) : (
              <div className="py-12 text-center">
                <p className="text-muted-foreground">No albums yet</p>
              </div>
            )}
          </div>
        )}

        {activeView === 'grid' && (
          <div className="mt-0">
            <GalleryGrid
              images={filteredImages}
              loading={loading}
              searchQuery={searchQuery}
              selectedCategory={selectedCategory}
              selectedTags={selectedTags}
            />
          </div>
        )}
      </div>
    </div>
  )
} 