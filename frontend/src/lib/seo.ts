// SEO相关API和工具函数
import { API_BASE_URL } from './api';

// SEO配置接口
export interface SeoConfig {
  title: string;
  description: string;
  keywords: string;
  canonical_url: string;
  robots: string;
  og_title: string;
  og_description: string;
  og_image: string;
  og_url: string;
  og_type: string;
  og_site_name: string;
  og_locale: string;
  twitter_card: string;
  twitter_site: string;
  twitter_creator: string;
  structured_data?: any;
}

// 获取页面SEO配置
export async function getPageSeoConfig(pageType: string): Promise<SeoConfig | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/seo/meta-tags/page/${pageType}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      console.error(`Failed to fetch SEO config for page ${pageType}:`, response.statusText);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching SEO config for page ${pageType}:`, error);
    return null;
  }
}

// 获取内容SEO配置
export async function getContentSeoConfig(pageType: string, contentId: number): Promise<SeoConfig | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/seo/meta-tags/content/${pageType}/${contentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 1800 } // 缓存30分钟
    });

    if (!response.ok) {
      console.error(`Failed to fetch SEO config for content ${pageType}/${contentId}:`, response.statusText);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching SEO config for content ${pageType}/${contentId}:`, error);
    return null;
  }
}

// 生成动态SEO配置
export async function generateSeoConfig(data: {
  page_type: string;
  content_id?: number;
  custom_data?: any;
}): Promise<SeoConfig | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/seo/meta-tags`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      next: { revalidate: 1800 } // 缓存30分钟
    });

    if (!response.ok) {
      console.error('Failed to generate SEO config:', response.statusText);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error generating SEO config:', error);
    return null;
  }
}

// 获取结构化数据
export async function getStructuredData(type: 'global' | 'blog' | 'project', slug?: string): Promise<any | null> {
  try {
    let url = `${API_BASE_URL}/seo/structured-data/${type}`;
    if (slug) {
      url += `/${slug}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      console.error(`Failed to fetch structured data for ${type}:`, response.statusText);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching structured data for ${type}:`, error);
    return null;
  }
}

// 动态获取当前域名
const getCurrentDomain = () => {
  // 优先使用环境变量
  if (process.env.NEXT_PUBLIC_SITE_URL) {
    return process.env.NEXT_PUBLIC_SITE_URL;
  }

  // 根据当前域名自动推断
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;
    const port = window.location.port;

    // 生产环境
    if (hostname.includes('your-domain.com')) {
      return `${protocol}//${hostname}`;
    }

    // 本地开发环境
    if (hostname === 'localhost' || hostname.startsWith('192.168') || hostname.startsWith('100.90')) {
      return port ? `${protocol}//${hostname}:${port}` : `${protocol}//${hostname}`;
    }
  }

  // 默认域名 - 使用环境变量或localhost
  return process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
};

// 默认SEO配置
export const defaultSeoConfig: SeoConfig = {
  title: 'Jingyao Chen - International Red Dot Award Designer & Developer',
  description: 'Portfolio of Jingyao Chen - International Red Dot Award designer and full-stack developer. Explore innovative projects, insightful blogs, and creative journey.',
  keywords: 'designer,developer,red dot award,portfolio,projects,blogs,web development,UI/UX design',
  canonical_url: getCurrentDomain(),
  robots: 'index,follow',
  og_title: 'Jingyao Chen - Award-Winning Designer & Developer',
  og_description: 'Discover the portfolio of an international Red Dot Award designer and full-stack developer. Innovation meets functionality.',
  og_image: '/images/og-default.jpg',
  og_url: getCurrentDomain(),
  og_type: 'website',
  og_site_name: 'Jingyao Chen Portfolio',
  og_locale: 'en_US',
  twitter_card: 'summary_large_image',
  twitter_site: '@JingyaoC',
  twitter_creator: '@JingyaoC'
};

// 合并SEO配置
export function mergeSeoConfig(base: SeoConfig, override: Partial<SeoConfig>): SeoConfig {
  return {
    ...base,
    ...override
  };
}

// 生成meta标签HTML
export function generateMetaTags(config: SeoConfig): string {
  const tags = [
    // 基础meta标签
    `<title>${config.title}</title>`,
    `<meta name="description" content="${config.description}" />`,
    `<meta name="keywords" content="${config.keywords}" />`,
    `<meta name="robots" content="${config.robots}" />`,
    `<link rel="canonical" href="${config.canonical_url}" />`,
    
    // Open Graph标签
    `<meta property="og:title" content="${config.og_title}" />`,
    `<meta property="og:description" content="${config.og_description}" />`,
    `<meta property="og:image" content="${config.og_image}" />`,
    `<meta property="og:url" content="${config.og_url}" />`,
    `<meta property="og:type" content="${config.og_type}" />`,
    `<meta property="og:site_name" content="${config.og_site_name}" />`,
    `<meta property="og:locale" content="${config.og_locale}" />`,
    
    // Twitter卡片标签
    `<meta name="twitter:card" content="${config.twitter_card}" />`,
    `<meta name="twitter:site" content="${config.twitter_site}" />`,
    `<meta name="twitter:creator" content="${config.twitter_creator}" />`,
    `<meta name="twitter:title" content="${config.og_title}" />`,
    `<meta name="twitter:description" content="${config.og_description}" />`,
    `<meta name="twitter:image" content="${config.og_image}" />`
  ];

  return tags.join('\n');
}

// 生成结构化数据脚本
export function generateStructuredDataScript(data: any): string {
  if (!data) return '';
  
  return `<script type="application/ld+json">
${JSON.stringify(data, null, 2)}
</script>`;
}

// 验证SEO配置
export function validateSeoConfig(config: SeoConfig): {
  isValid: boolean;
  warnings: string[];
  errors: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];

  // 标题长度检查
  if (config.title.length < 30) {
    warnings.push('标题长度过短，建议30-60个字符');
  } else if (config.title.length > 60) {
    warnings.push('标题长度过长，建议30-60个字符');
  }

  // 描述长度检查
  if (config.description.length < 120) {
    warnings.push('描述长度过短，建议120-160个字符');
  } else if (config.description.length > 160) {
    warnings.push('描述长度过长，建议120-160个字符');
  }

  // 必填字段检查
  if (!config.title) {
    errors.push('标题不能为空');
  }
  if (!config.description) {
    errors.push('描述不能为空');
  }
  if (!config.canonical_url) {
    errors.push('规范URL不能为空');
  }

  // URL格式检查
  try {
    new URL(config.canonical_url);
  } catch {
    errors.push('规范URL格式不正确');
  }

  try {
    new URL(config.og_image);
  } catch {
    warnings.push('OG图片URL格式可能不正确');
  }

  return {
    isValid: errors.length === 0,
    warnings,
    errors
  };
}

// 页面类型映射
export const pageTypeMap: Record<string, string> = {
  '/': 'homepage',
  '/about': 'about',
  '/projects': 'projects',
  '/blogs': 'blogs',
  '/gallery': 'gallery'
};

// 根据路径获取页面类型
export function getPageTypeFromPath(path: string): string {
  // 精确匹配
  if (pageTypeMap[path]) {
    return pageTypeMap[path];
  }

  // 模式匹配
  if (path.startsWith('/blogs/')) {
    return 'blog';
  }
  if (path.startsWith('/projects/')) {
    return 'project';
  }
  if (path.startsWith('/gallery/')) {
    return 'gallery';
  }

  // 默认返回通用页面类型
  return 'page';
}
