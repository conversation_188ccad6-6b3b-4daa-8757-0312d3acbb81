globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/projects/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/blog/BlogPreloader.tsx":{"*":{"id":"(ssr)/./src/components/blog/BlogPreloader.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/project/ProjectCard.tsx":{"*":{"id":"(ssr)/./src/components/project/ProjectCard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/AntdIcon.tsx":{"*":{"id":"(ssr)/./src/components/shared/AntdIcon.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/CustomIcon.tsx":{"*":{"id":"(ssr)/./src/components/shared/CustomIcon.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/enhanced-interactions.tsx":{"*":{"id":"(ssr)/./src/components/ui/enhanced-interactions.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/page-transition.tsx":{"*":{"id":"(ssr)/./src/components/ui/page-transition.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(ssr)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/analytics/google-analytics.tsx":{"*":{"id":"(ssr)/./src/components/analytics/google-analytics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/analytics/plausible-analytics.tsx":{"*":{"id":"(ssr)/./src/components/analytics/plausible-analytics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ErrorBoundary.tsx":{"*":{"id":"(ssr)/./src/components/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.tsx":{"*":{"id":"(ssr)/./src/components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/performance/PerformanceMonitor.tsx":{"*":{"id":"(ssr)/./src/components/performance/PerformanceMonitor.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme/ThemeInitializer.tsx":{"*":{"id":"(ssr)/./src/components/theme/ThemeInitializer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/hooks/useSmartPrefetch.ts":{"*":{"id":"(ssr)/./src/hooks/useSmartPrefetch.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/blog/EnhancedBlogList.tsx":{"*":{"id":"(ssr)/./src/components/blog/EnhancedBlogList.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/ProjectsClient.tsx":{"*":{"id":"(ssr)/./src/app/projects/ProjectsClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/shared/LazyWrapper.tsx":{"*":{"id":"(ssr)/./src/components/shared/LazyWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/gallery/EnhancedGalleryContainer.tsx":{"*":{"id":"(ssr)/./src/components/gallery/EnhancedGalleryContainer.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/blog/BlogPreloader.tsx":{"id":"(app-pages-browser)/./src/components/blog/BlogPreloader.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/home/<USER>":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/home/<USER>":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/home/<USER>":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/home/<USER>":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/project/ProjectCard.tsx":{"id":"(app-pages-browser)/./src/components/project/ProjectCard.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/shared/AntdIcon.tsx":{"id":"(app-pages-browser)/./src/components/shared/AntdIcon.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/shared/CustomIcon.tsx":{"id":"(app-pages-browser)/./src/components/shared/CustomIcon.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/ui/enhanced-interactions.tsx":{"id":"(app-pages-browser)/./src/components/ui/enhanced-interactions.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/ui/page-transition.tsx":{"id":"(app-pages-browser)/./src/components/ui/page-transition.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/client/script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/client/script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/app/providers.tsx":{"id":"(app-pages-browser)/./src/app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/analytics/google-analytics.tsx":{"id":"(app-pages-browser)/./src/components/analytics/google-analytics.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/analytics/plausible-analytics.tsx":{"id":"(app-pages-browser)/./src/components/analytics/plausible-analytics.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/ErrorBoundary.tsx":{"id":"(app-pages-browser)/./src/components/ErrorBoundary.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/layout/Footer.tsx":{"id":"(app-pages-browser)/./src/components/layout/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/layout/Header.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/performance/PerformanceMonitor.tsx":{"id":"(app-pages-browser)/./src/components/performance/PerformanceMonitor.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/theme/ThemeInitializer.tsx":{"id":"(app-pages-browser)/./src/components/theme/ThemeInitializer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/hooks/useSmartPrefetch.ts":{"id":"(app-pages-browser)/./src/hooks/useSmartPrefetch.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/styles/tailwind.css":{"id":"(app-pages-browser)/./src/styles/tailwind.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/projects/page","static/chunks/app/projects/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":["app/projects/page","static/chunks/app/projects/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/projects/page","static/chunks/app/projects/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/node_modules/next/dist/esm/shared/lib/lazy-dynamic/preload-css.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js","name":"*","chunks":["app/projects/page","static/chunks/app/projects/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/blog/EnhancedBlogList.tsx":{"id":"(app-pages-browser)/./src/components/blog/EnhancedBlogList.tsx","name":"*","chunks":[],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/app/projects/ProjectsClient.tsx":{"id":"(app-pages-browser)/./src/app/projects/ProjectsClient.tsx","name":"*","chunks":["app/projects/page","static/chunks/app/projects/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/shared/LazyWrapper.tsx":{"id":"(app-pages-browser)/./src/components/shared/LazyWrapper.tsx","name":"*","chunks":["app/projects/page","static/chunks/app/projects/page.js"],"async":false},"/home/<USER>/Code/me/My-web/frontend/src/components/gallery/EnhancedGalleryContainer.tsx":{"id":"(app-pages-browser)/./src/components/gallery/EnhancedGalleryContainer.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/home/<USER>/Code/me/My-web/frontend/src/":[],"/home/<USER>/Code/me/My-web/frontend/src/app/page":[],"/home/<USER>/Code/me/My-web/frontend/src/app/layout":["static/css/app/layout.css"],"/home/<USER>/Code/me/My-web/frontend/src/app/not-found":[],"/home/<USER>/Code/me/My-web/frontend/src/app/projects/page":[]}}